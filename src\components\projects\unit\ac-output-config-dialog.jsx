import React, { useState, useEffect, useCallback, memo, useMemo } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Combobox } from "@/components/ui/combobox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Thermometer, Settings, Wind, Gauge } from "lucide-react";

// Configuration options based on WinForms implementation
const WINDOWS_MODE_OPTIONS = [
  { value: "0", label: "Off" },
  { value: "1", label: "Save energy" },
];

const FAN_TYPE_OPTIONS = [
  { value: "0", label: "On/Off" },
  { value: "1", label: "Analog" },
];

const TEMP_TYPE_OPTIONS = [
  { value: "0", label: "Thermostat" },
  { value: "1", label: "RCU" },
];

const TEMP_UNIT_OPTIONS = [
  { value: "0", label: "°C" },
  { value: "1", label: "°F" },
];

const VALVE_CONTACT_OPTIONS = [
  { value: "0", label: "NO" },
  { value: "1", label: "NC" },
];

const VALVE_TYPE_OPTIONS = [
  { value: "0", label: "On/Off" },
  { value: "1", label: "Analog" },
  { value: "2", label: "On and Off" },
];

const DEAD_BAND_OPTIONS = [
  { value: "1.0", label: "1.0" },
  { value: "1.5", label: "1.5" },
  { value: "2.0", label: "2.0" },
  { value: "2.5", label: "2.5" },
  { value: "3.0", label: "3.0" },
  { value: "3.5", label: "3.5" },
  { value: "4.0", label: "4.0" },
  { value: "4.5", label: "4.5" },
  { value: "5.0", label: "5.0" },
  { value: "5.5", label: "5.5" },
  { value: "6.0", label: "6.0" },
  { value: "6.5", label: "6.5" },
  { value: "7.0", label: "7.0" },
  { value: "7.5", label: "7.5" },
  { value: "8.0", label: "8.0" },
  { value: "8.5", label: "8.5" },
  { value: "9.0", label: "9.0" },
  { value: "9.5", label: "9.5" },
  { value: "10", label: "10" },
];

const WINDOWS_OPTIONS = [
  { value: "0", label: "Normal" },
  { value: "1", label: "Bypass" },
];

// Memoized Select component for better performance
const MemoizedSelect = memo(({ value, onValueChange, options, className }) => (
  <Select value={value} onValueChange={onValueChange}>
    <SelectTrigger className={className}>
      <SelectValue />
    </SelectTrigger>
    <SelectContent>
      {options.map((option) => (
        <SelectItem key={option.value} value={option.value}>
          {option.label}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
));

// Memoized Combobox component for better performance
const MemoizedCombobox = memo(
  ({ value, onValueChange, options, placeholder, emptyText }) => (
    <Combobox
      options={options}
      value={value}
      onValueChange={onValueChange}
      placeholder={placeholder}
      emptyText={emptyText}
    />
  )
);

export const ACOutputConfigDialog = memo(
  ({
    open,
    onOpenChange,
    outputName = "",
    initialConfig = {},
    lightingOptions = [],
    onSave,
  }) => {
    // State for all AC configuration options
    const [config, setConfig] = useState({
      enable: false,
      windowsMode: "0",
      fanType: "0",
      tempType: "0",
      tempUnit: "0",
      valveContact: "0",
      valveType: "0",
      deadBand: "1.0",
      windows: "0",
      lowFan: null,
      medFan: null,
      highFan: null,
      analogFan: null,
      analogCool: null,
      analogHeat: null,
      coolOpen: null,
      coolClose: null,
      heatOpen: null,
      heatClose: null,
    });

    const [loading, setLoading] = useState(false);

    // Memoize lighting options to prevent recalculation
    const memoizedLightingOptions = useMemo(
      () => lightingOptions,
      [lightingOptions]
    );

    // Initialize config from props - update whenever initialConfig changes
    useEffect(() => {
      if (open) {
        setConfig({
          enable: initialConfig.enable || false,
          windowsMode: initialConfig.windowsMode?.toString() || "0",
          fanType: initialConfig.fanType?.toString() || "0",
          tempType: initialConfig.tempType?.toString() || "0",
          tempUnit: initialConfig.tempUnit?.toString() || "0",
          valveContact: initialConfig.valveContact?.toString() || "0",
          valveType: initialConfig.valveType?.toString() || "0",
          deadBand: initialConfig.deadBand?.toString() || "1.0",
          windows: initialConfig.windows?.toString() || "0",
          lowFan: initialConfig.lowFan || null,
          medFan: initialConfig.medFan || null,
          highFan: initialConfig.highFan || null,
          analogFan: initialConfig.analogFan || null,
          analogCool: initialConfig.analogCool || null,
          analogHeat: initialConfig.analogHeat || null,
          coolOpen: initialConfig.coolOpen || null,
          coolClose: initialConfig.coolClose || null,
          heatOpen: initialConfig.heatOpen || null,
          heatClose: initialConfig.heatClose || null,
        });
      }
    }, [open, initialConfig]);

    // Memoized handlers to prevent unnecessary re-renders
    const handleClose = useCallback(() => {
      onOpenChange(false);
    }, [onOpenChange]);

    const handleSave = useCallback(async () => {
      setLoading(true);
      try {
        // Convert string values back to appropriate types
        const configToSave = {
          ...config,
          windowsMode: parseInt(config.windowsMode),
          fanType: parseInt(config.fanType),
          tempType: parseInt(config.tempType),
          tempUnit: parseInt(config.tempUnit),
          valveContact: parseInt(config.valveContact),
          valveType: parseInt(config.valveType),
          deadBand: parseFloat(config.deadBand),
          windows: parseInt(config.windows),
        };

        await onSave(configToSave);
        handleClose();
      } catch (error) {
        console.error("Failed to save AC output configuration:", error);
      } finally {
        setLoading(false);
      }
    }, [config, onSave, handleClose]);

    // Optimized update handlers for each field
    const updateEnable = useCallback((checked) => {
      setConfig((prev) => ({ ...prev, enable: checked }));
    }, []);

    const updateWindowsMode = useCallback((value) => {
      setConfig((prev) => ({ ...prev, windowsMode: value }));
    }, []);

    const updateFanType = useCallback((value) => {
      setConfig((prev) => ({ ...prev, fanType: value }));
    }, []);

    const updateTempType = useCallback((value) => {
      setConfig((prev) => ({ ...prev, tempType: value }));
    }, []);

    const updateTempUnit = useCallback((value) => {
      setConfig((prev) => ({ ...prev, tempUnit: value }));
    }, []);

    const updateValveContact = useCallback((value) => {
      setConfig((prev) => ({ ...prev, valveContact: value }));
    }, []);

    const updateValveType = useCallback((value) => {
      setConfig((prev) => ({ ...prev, valveType: value }));
    }, []);

    const updateDeadBand = useCallback((value) => {
      setConfig((prev) => ({ ...prev, deadBand: value }));
    }, []);

    const updateWindows = useCallback((value) => {
      setConfig((prev) => ({ ...prev, windows: value }));
    }, []);

    const updateLowFan = useCallback((value) => {
      setConfig((prev) => ({
        ...prev,
        lowFan: value ? parseInt(value) : null,
      }));
    }, []);

    const updateMedFan = useCallback((value) => {
      setConfig((prev) => ({
        ...prev,
        medFan: value ? parseInt(value) : null,
      }));
    }, []);

    const updateHighFan = useCallback((value) => {
      setConfig((prev) => ({
        ...prev,
        highFan: value ? parseInt(value) : null,
      }));
    }, []);

    const updateAnalogFan = useCallback((value) => {
      setConfig((prev) => ({
        ...prev,
        analogFan: value ? parseInt(value) : null,
      }));
    }, []);

    const updateAnalogCool = useCallback((value) => {
      setConfig((prev) => ({
        ...prev,
        analogCool: value ? parseInt(value) : null,
      }));
    }, []);

    const updateAnalogHeat = useCallback((value) => {
      setConfig((prev) => ({
        ...prev,
        analogHeat: value ? parseInt(value) : null,
      }));
    }, []);

    const updateCoolOpen = useCallback((value) => {
      setConfig((prev) => ({
        ...prev,
        coolOpen: value ? parseInt(value) : null,
      }));
    }, []);

    const updateCoolClose = useCallback((value) => {
      setConfig((prev) => ({
        ...prev,
        coolClose: value ? parseInt(value) : null,
      }));
    }, []);

    const updateHeatOpen = useCallback((value) => {
      setConfig((prev) => ({
        ...prev,
        heatOpen: value ? parseInt(value) : null,
      }));
    }, []);

    const updateHeatClose = useCallback((value) => {
      setConfig((prev) => ({
        ...prev,
        heatClose: value ? parseInt(value) : null,
      }));
    }, []);

    // Only render dialog content when open for better performance
    if (!open) {
      return (
        <Dialog open={open} onOpenChange={onOpenChange}>
          <DialogContent />
        </Dialog>
      );
    }

    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Thermometer className="h-5 w-5" />
              AC Output Configuration
            </DialogTitle>
            <DialogDescription>
              Configure air conditioning settings for {outputName}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Enable Checkbox */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="enable"
                checked={config.enable}
                onCheckedChange={updateEnable}
              />
              <Label htmlFor="enable" className="text-sm font-medium">
                Enable
              </Label>
            </div>

            {/* Basic Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Settings className="h-4 w-4" />
                  Basic Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Windows mode</Label>
                    <MemoizedSelect
                      value={config.windowsMode}
                      onValueChange={updateWindowsMode}
                      options={WINDOWS_MODE_OPTIONS}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Fan type</Label>
                    <MemoizedSelect
                      value={config.fanType}
                      onValueChange={updateFanType}
                      options={FAN_TYPE_OPTIONS}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Temp type</Label>
                    <MemoizedSelect
                      value={config.tempType}
                      onValueChange={updateTempType}
                      options={TEMP_TYPE_OPTIONS}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Temp Unit</Label>
                    <MemoizedSelect
                      value={config.tempUnit}
                      onValueChange={updateTempUnit}
                      options={TEMP_UNIT_OPTIONS}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Valve contact</Label>
                    <MemoizedSelect
                      value={config.valveContact}
                      onValueChange={updateValveContact}
                      options={VALVE_CONTACT_OPTIONS}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Valve type</Label>
                    <MemoizedSelect
                      value={config.valveType}
                      onValueChange={updateValveType}
                      options={VALVE_TYPE_OPTIONS}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Dead band</Label>
                    <MemoizedSelect
                      value={config.deadBand}
                      onValueChange={updateDeadBand}
                      options={DEAD_BAND_OPTIONS}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Windows</Label>
                    <MemoizedSelect
                      value={config.windows}
                      onValueChange={updateWindows}
                      options={WINDOWS_OPTIONS}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Fan Groups Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Wind className="h-4 w-4" />
                  Fan Groups
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Low fan</Label>
                    <MemoizedCombobox
                      options={memoizedLightingOptions}
                      value={config.lowFan?.toString() || ""}
                      onValueChange={updateLowFan}
                      placeholder="Select lighting group..."
                      emptyText="No lighting groups found"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Med fan</Label>
                    <MemoizedCombobox
                      options={memoizedLightingOptions}
                      value={config.medFan?.toString() || ""}
                      onValueChange={updateMedFan}
                      placeholder="Select lighting group..."
                      emptyText="No lighting groups found"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">High fan</Label>
                    <MemoizedCombobox
                      options={memoizedLightingOptions}
                      value={config.highFan?.toString() || ""}
                      onValueChange={updateHighFan}
                      placeholder="Select lighting group..."
                      emptyText="No lighting groups found"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Analog fan</Label>
                    <MemoizedCombobox
                      options={memoizedLightingOptions}
                      value={config.analogFan?.toString() || ""}
                      onValueChange={updateAnalogFan}
                      placeholder="Select lighting group..."
                      emptyText="No lighting groups found"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Analog cool</Label>
                    <MemoizedCombobox
                      options={memoizedLightingOptions}
                      value={config.analogCool?.toString() || ""}
                      onValueChange={updateAnalogCool}
                      placeholder="Select lighting group..."
                      emptyText="No lighting groups found"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Analog heat</Label>
                    <MemoizedCombobox
                      options={memoizedLightingOptions}
                      value={config.analogHeat?.toString() || ""}
                      onValueChange={updateAnalogHeat}
                      placeholder="Select lighting group..."
                      emptyText="No lighting groups found"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Valve Groups Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Gauge className="h-4 w-4" />
                  Valve Groups
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Cool open</Label>
                    <MemoizedCombobox
                      options={memoizedLightingOptions}
                      value={config.coolOpen?.toString() || ""}
                      onValueChange={updateCoolOpen}
                      placeholder="Select lighting group..."
                      emptyText="No lighting groups found"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Cool close</Label>
                    <MemoizedCombobox
                      options={memoizedLightingOptions}
                      value={config.coolClose?.toString() || ""}
                      onValueChange={updateCoolClose}
                      placeholder="Select lighting group..."
                      emptyText="No lighting groups found"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Heat open</Label>
                    <MemoizedCombobox
                      options={memoizedLightingOptions}
                      value={config.heatOpen?.toString() || ""}
                      onValueChange={updateHeatOpen}
                      placeholder="Select lighting group..."
                      emptyText="No lighting groups found"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Heat close</Label>
                    <MemoizedCombobox
                      options={memoizedLightingOptions}
                      value={config.heatClose?.toString() || ""}
                      onValueChange={updateHeatClose}
                      placeholder="Select lighting group..."
                      emptyText="No lighting groups found"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleClose} disabled={loading}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={loading}>
              {loading ? "Saving..." : "Save Configuration"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }
);
