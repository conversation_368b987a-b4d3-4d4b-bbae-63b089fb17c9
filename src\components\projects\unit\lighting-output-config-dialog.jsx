import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo, memo } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Lightbulb, Clock, Timer, Zap } from "lucide-react";

// Pre-computed time options for better performance
const HOUR_OPTIONS = Array.from({ length: 19 }, (_, i) => ({
  value: i.toString(),
  label: i.toString(),
}));

const MINUTE_OPTIONS = Array.from({ length: 60 }, (_, i) => ({
  value: i.toString(),
  label: i.toString(),
}));

const SECOND_OPTIONS = Array.from({ length: 60 }, (_, i) => ({
  value: i.toString(),
  label: i.toString(),
}));

const SCHEDULE_HOUR_OPTIONS = Array.from({ length: 24 }, (_, i) => ({
  value: i.toString(),
  label: i.toString(),
}));

const MIN_DIM_OPTIONS = Array.from({ length: 30 }, (_, i) => ({
  value: (i + 1).toString(),
  label: (i + 1).toString(),
}));

const MAX_DIM_OPTIONS = Array.from({ length: 31 }, (_, i) => ({
  value: (i + 70).toString(),
  label: (i + 70).toString(),
}));

// Memoized Select component for better performance
const MemoizedSelect = memo(({ value, onValueChange, options, className }) => (
  <Select value={value} onValueChange={onValueChange}>
    <SelectTrigger className={className}>
      <SelectValue />
    </SelectTrigger>
    <SelectContent>
      {options.map((option) => (
        <SelectItem key={option.value} value={option.value}>
          {option.label}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
));

// Memoized time selector component
const TimeSelector = memo(
  ({
    hourValue,
    minuteValue,
    secondValue,
    onHourChange,
    onMinuteChange,
    onSecondChange,
    showSeconds = true,
    isSchedule = false,
  }) => (
    <div className="flex items-center gap-2">
      <MemoizedSelect
        value={hourValue.toString()}
        onValueChange={(value) => onHourChange(parseInt(value))}
        options={isSchedule ? SCHEDULE_HOUR_OPTIONS : HOUR_OPTIONS}
        className="w-20"
      />
      <Label className="text-sm">{isSchedule ? "hour" : "hours"}</Label>

      <MemoizedSelect
        value={minuteValue.toString()}
        onValueChange={(value) => onMinuteChange(parseInt(value))}
        options={MINUTE_OPTIONS}
        className="w-20"
      />
      <Label className="text-sm">{isSchedule ? "min" : "mins"}</Label>

      {showSeconds && (
        <>
          <MemoizedSelect
            value={secondValue.toString()}
            onValueChange={(value) => onSecondChange(parseInt(value))}
            options={SECOND_OPTIONS}
            className="w-20"
          />
          <Label className="text-sm">secs</Label>
        </>
      )}
    </div>
  )
);

export const LightingOutputConfigDialog = memo(
  ({
    open,
    onOpenChange,
    outputName = "",
    outputType = "",
    initialConfig = {},
    onSave,
  }) => {
    // State for all configuration options
    const [config, setConfig] = useState({
      delayOffHours: 0,
      delayOffMinutes: 0,
      delayOffSeconds: 0,
      delayOnHours: 0,
      delayOnMinutes: 0,
      delayOnSeconds: 0,
      minDim: 1,
      maxDim: 100,
      autoTrigger: false,
      scheduleOnHour: 0,
      scheduleOnMinute: 0,
      scheduleOffHour: 0,
      scheduleOffMinute: 0,
    });

    const [loading, setLoading] = useState(false);

    // Check if this is a dimmer output (shows min/max dim options)
    const isDimmerOutput = useMemo(() => {
      return outputType === "dimmer";
    }, [outputType]);

    // Initialize config from props - only when dialog opens
    useEffect(() => {
      if (open && initialConfig) {
        setConfig({
          delayOffHours: initialConfig.delayOffHours || 0,
          delayOffMinutes: initialConfig.delayOffMinutes || 0,
          delayOffSeconds: initialConfig.delayOffSeconds || 0,
          delayOnHours: initialConfig.delayOnHours || 0,
          delayOnMinutes: initialConfig.delayOnMinutes || 0,
          delayOnSeconds: initialConfig.delayOnSeconds || 0,
          minDim: initialConfig.minDim || 1,
          maxDim: initialConfig.maxDim || 100,
          autoTrigger: initialConfig.autoTrigger || false,
          scheduleOnHour: initialConfig.scheduleOnHour || 0,
          scheduleOnMinute: initialConfig.scheduleOnMinute || 0,
          scheduleOffHour: initialConfig.scheduleOffHour || 0,
          scheduleOffMinute: initialConfig.scheduleOffMinute || 0,
        });
      }
    }, [open, initialConfig]);

    // Memoized handlers to prevent unnecessary re-renders
    const handleClose = useCallback(() => {
      onOpenChange(false);
    }, [onOpenChange]);

    const handleSave = useCallback(async () => {
      setLoading(true);
      try {
        await onSave(config);
        handleClose();
      } catch (error) {
        console.error("Failed to save lighting output configuration:", error);
      } finally {
        setLoading(false);
      }
    }, [config, onSave, handleClose]);

    // Optimized update handlers for each field
    const updateDelayOffHours = useCallback((value) => {
      setConfig((prev) => ({ ...prev, delayOffHours: value }));
    }, []);

    const updateDelayOffMinutes = useCallback((value) => {
      setConfig((prev) => ({ ...prev, delayOffMinutes: value }));
    }, []);

    const updateDelayOffSeconds = useCallback((value) => {
      setConfig((prev) => ({ ...prev, delayOffSeconds: value }));
    }, []);

    const updateDelayOnHours = useCallback((value) => {
      setConfig((prev) => ({ ...prev, delayOnHours: value }));
    }, []);

    const updateDelayOnMinutes = useCallback((value) => {
      setConfig((prev) => ({ ...prev, delayOnMinutes: value }));
    }, []);

    const updateDelayOnSeconds = useCallback((value) => {
      setConfig((prev) => ({ ...prev, delayOnSeconds: value }));
    }, []);

    const updateScheduleOnHour = useCallback((value) => {
      setConfig((prev) => ({ ...prev, scheduleOnHour: value }));
    }, []);

    const updateScheduleOnMinute = useCallback((value) => {
      setConfig((prev) => ({ ...prev, scheduleOnMinute: value }));
    }, []);

    const updateScheduleOffHour = useCallback((value) => {
      setConfig((prev) => ({ ...prev, scheduleOffHour: value }));
    }, []);

    const updateScheduleOffMinute = useCallback((value) => {
      setConfig((prev) => ({ ...prev, scheduleOffMinute: value }));
    }, []);

    const updateMinDim = useCallback((value) => {
      setConfig((prev) => ({ ...prev, minDim: parseInt(value) }));
    }, []);

    const updateMaxDim = useCallback((value) => {
      setConfig((prev) => ({ ...prev, maxDim: parseInt(value) }));
    }, []);

    const updateAutoTrigger = useCallback((checked) => {
      setConfig((prev) => ({ ...prev, autoTrigger: checked }));
    }, []);

    // Only render dialog content when open for better performance
    if (!open) {
      return (
        <Dialog open={open} onOpenChange={onOpenChange}>
          <DialogContent />
        </Dialog>
      );
    }

    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              Lighting Output Configuration
            </DialogTitle>
            <DialogDescription>
              Configure timing and dimming settings for {outputName}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Delay Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Timer className="h-4 w-4" />
                  Delay Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Delay Off Output */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Delay off output:
                  </Label>
                  <TimeSelector
                    hourValue={config.delayOffHours}
                    minuteValue={config.delayOffMinutes}
                    secondValue={config.delayOffSeconds}
                    onHourChange={updateDelayOffHours}
                    onMinuteChange={updateDelayOffMinutes}
                    onSecondChange={updateDelayOffSeconds}
                  />
                </div>

                {/* Delay On Output */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Delay on output:
                  </Label>
                  <TimeSelector
                    hourValue={config.delayOnHours}
                    minuteValue={config.delayOnMinutes}
                    secondValue={config.delayOnSeconds}
                    onHourChange={updateDelayOnHours}
                    onMinuteChange={updateDelayOnMinutes}
                    onSecondChange={updateDelayOnSeconds}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Dimming Configuration - Only for dimmer outputs */}
            {isDimmerOutput && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-base">
                    <Zap className="h-4 w-4" />
                    Dimming Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Min Dim (%)</Label>
                      <MemoizedSelect
                        value={config.minDim.toString()}
                        onValueChange={updateMinDim}
                        options={MIN_DIM_OPTIONS.map((opt) => ({
                          ...opt,
                          label: `${opt.label}%`,
                        }))}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Max Dim (%)</Label>
                      <MemoizedSelect
                        value={config.maxDim.toString()}
                        onValueChange={updateMaxDim}
                        options={MAX_DIM_OPTIONS.map((opt) => ({
                          ...opt,
                          label: `${opt.label}%`,
                        }))}
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="autoTrigger"
                      checked={config.autoTrigger}
                      onCheckedChange={updateAutoTrigger}
                    />
                    <Label
                      htmlFor="autoTrigger"
                      className="text-sm font-medium"
                    >
                      Auto trigger
                    </Label>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Schedule Configuration */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-base">
                  <Clock className="h-4 w-4" />
                  Schedule Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Schedule On */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Schedule on at:</Label>
                  <TimeSelector
                    hourValue={config.scheduleOnHour}
                    minuteValue={config.scheduleOnMinute}
                    secondValue={0}
                    onHourChange={updateScheduleOnHour}
                    onMinuteChange={updateScheduleOnMinute}
                    onSecondChange={() => {}}
                    showSeconds={false}
                    isSchedule={true}
                  />
                </div>

                {/* Schedule Off */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">
                    Schedule off at:
                  </Label>
                  <TimeSelector
                    hourValue={config.scheduleOffHour}
                    minuteValue={config.scheduleOffMinute}
                    secondValue={0}
                    onHourChange={updateScheduleOffHour}
                    onMinuteChange={updateScheduleOffMinute}
                    onSecondChange={() => {}}
                    showSeconds={false}
                    isSchedule={true}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleClose} disabled={loading}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={loading}>
              {loading ? "Saving..." : "Save Configuration"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }
);
